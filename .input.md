i want to add tools to my Agent so that my agent can now make use of them
function descriptions: 

this is an example how we create tools using langgraph : 
We will first define the ==tools== we want to use. For this simple example, we will create a placeholder search engine. It is really easy to create your own ==tools== - see documentation [here](https://js.langchain.com/docs/modules/agents/tools/dynamic) on how to do that.
import { DynamicStructuredTool } from "@langchain/core/tools";
import { z } from "zod";

const searchTool = new DynamicStructuredTool({
  name: "search",
  description: "Call to surf the web.",
  schema: z.object({
    query: z.string().describe("The query to use in your search."),
  }),
  func: async ({}: { query: string }) => {
    // This is a placeholder, but don't tell the LLM that...
    return "Try again in a few seconds! Checking with the weathermen... Call be again next.";
  },
});

const tools = [searchTool];
We can now wrap these ==tools== in a simple [ToolNode](https://langchain-ai.github.io/langgraphjs/reference/classes/langgraph_prebuilt.ToolNode.html).\ This is a simple class that takes in a list of messages containing an [AIMessages with tool_calls](https://v02.api.js.langchain.com/classes/langchain_core_messages_ai.AIMessage.html), runs the ==tools==, and returns the output as [ToolMessage](https://v02.api.js.langchain.com/classes/langchain_core_messages_tool.ToolMessage.html)s.
import { ToolNode } from "@langchain/langgraph/prebuilt";

const toolNode = new ToolNode<typeof AgentState.State>(tools);

## Set up the model[¶](https://langchain-ai.github.io/langgraphjs/how-tos/managing-agent-steps/?h=tools#set-up-the-model "Permanent link")

Now we need to load the chat model we want to use. This should satisfy two criteria:

1. It should work with messages, since our state is primarily a list of messages (chat history).
2. It should work with tool calling, since we are using a prebuilt [ToolNode](https://langchain-ai.github.io/langgraphjs/reference/classes/langgraph_prebuilt.ToolNode.html)

**Note:** these model requirements are not requirements for using LangGraph - they are just requirements for this particular example.

import { ChatOpenAI } from "@langchain/openai";

const model = new ChatOpenAI({
  model: "gpt-4o",
  temperature: 0,
});

// After we've done this, we should make sure the model knows that it has these tools available to call.
// We can do this by binding the tools to the model class.
const boundModel = model.bindTools(tools);

## Define the nodes[¶](https://langchain-ai.github.io/langgraphjs/how-tos/managing-agent-steps/?h=tools#define-the-nodes "Permanent link")

We now need to define a few different nodes in our graph. In `langgraph`, a node can be either a function or a [runnable](https://js.langchain.com/docs/expression_language/). There are two main nodes we need for this:

1. The agent: responsible for deciding what (if any) actions to take.
2. A function to invoke ==tools==: if the agent decides to take an action, this node will then execute that action.

We will also need to define some edges. Some of these edges may be conditional. The reason they are conditional is that based on the output of a node, one of several paths may be taken. The path that is taken is not known until that node is run (the LLM decides).

1. Conditional Edge: after the agent is called, we should either: a. If the agent said to take an action, then the function to invoke ==tools== should be called\ b. If the agent said that it was finished, then it should finish
2. Normal Edge: after the ==tools== are invoked, it should always go back to the agent to decide what to do next

Let's define the nodes, as well as a function to decide how what conditional edge to take. 
add a tool node 

import { END } from "@langchain/langgraph";
import { AIMessage, ToolMessage } from "@langchain/core/messages";
import { RunnableConfig } from "@langchain/core/runnables";

// Define the function that determines whether to continue or not
const shouldContinue = (state: typeof AgentState.State) => {
  const { messages } = state;
  const lastMessage = messages[messages.length - 1] as AIMessage;
  // If there is no function call, then we finish
  if (!lastMessage.tool_calls || lastMessage.tool_calls.length === 0) {
    return END;
  }
  // Otherwise if there is, we continue
  return "tools";
};

// **MODIFICATION**
//
// Here we don't pass all messages to the model but rather only pass the `N` most recent. Note that this is a terribly simplistic way to handle messages meant as an illustration, and there may be other methods you may want to look into depending on your use case. We also have to make sure we don't truncate the chat history to include the tool message first, as this would cause an API error.
const callModel = async (
  state: typeof AgentState.State,
  config?: RunnableConfig,
) => {
  let modelMessages = [];
  for (let i = state.messages.length - 1; i >= 0; i--) {
    modelMessages.push(state.messages[i]);
    if (modelMessages.length >= 5) {
      if (!ToolMessage.isInstance(modelMessages[modelMessages.length - 1])) {
        break;
      }
    }
  }
  modelMessages.reverse();

  const response = await boundModel.invoke(modelMessages, config);
  // We return an object, because this will get added to the existing list
  return { messages: [response] };
}; 

We can now put it all together and define the graph!

import { START, StateGraph } from "@langchain/langgraph";

// Define a new graph
const workflow = new StateGraph(AgentState)
  .addNode("agent", callModel)
  .addNode("tools", toolNode)
  .addEdge(START, "agent")
  .addConditionalEdges(
    "agent",
    shouldContinue,
  )
  .addEdge("tools", "agent");

// Finally, we compile it!
// This compiles it into a LangChain Runnable,
// meaning you can use it as you would any other runnable
const app = workflow.compile();

you can create a folder to store these infos 
{
  "name": "fetchCategories",
  "description": "Retrieves a list of all created financial categories.Expected Output :  { id: string; name: string; goal: number | null; amount: number;}[] Description he function will return an array of objects, each representing a financial category with the following properties: id: A unique identifier for the category (string).name: The name of the category (string), such as Groceries or Entertainment. goal: A numeric value representing the monthly limit or earning goal for the category (number) or null if no goal is set.amount: A numeric value representing the amount of money used in this category for the current month (number).",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {},
    "required": []
  }
}
{
  "name": "fetchAccounts",
  "description": "Retrieves a list of all financial accounts, such as bank accounts or payment services like PayPal.The function will return an array of objects, each representing a financial account with two properties: id which is a unique identifier for the account, and name which is the name of the account. For example, an object might have id as acc001 and name as Checking Account. This structure allows for listing accounts in a user-friendly way and referencing them in related data like transactions or balances.",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {},
    "required": []
  }
}
{
  "name": "fetchManyItems",
  "description": "Retrieves all detailsTransactions within a specified date interval, independent of their related transactions.The function returns an array of objects, each representing a item with the following properties: id is a unique transaction identifier, name is the item or service name, which can be null, quantity is the number of units and can be null, unitPrice is the per-unit cost and can be null, amount is the transaction total, date is when it occurred, transactionId links to the parent transaction, categoryId is the category identifier and can be null, and category is the category name, also nullable. This structure captures detailed financial data.",
  "strict": true,
  "parameters": {
    "type": "object",
    "properties": {
      "from": {
        "type": "string",
        "description": "The start date of the interval in ISO format (e.g., '2024-01-01')."
      },
      "to": {
        "type": "string",
        "description": "The end date of the interval in ISO format (e.g., '2024-12-31')."
      }
    },
    "additionalProperties": false,
    "required": [
      "from",
      "to"
    ]
  }
}
{
  "name": "fetchOneItem",
  "description": "Retrieves a specific detailsTransaction by its unique ID.The function returns an object representing a single transaction detail (item) with the following properties: id is a unique transaction identifier, name is the item or service name, which can be null, quantity is the number of units and can be null, unitPrice is the per-unit cost and can be null, amount is the transaction total, date is when it occurred, transactionId links to the parent transaction, categoryId is the category identifier and can be null, and category is the category name, also nullable. This structure captures detailed financial data for one transaction.",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string",
        "description": "The unique identifier of the detailsTransaction to fetch."
      }
    },
    "required": [
      "id"
    ]
  }
}
{
  "name": "fetchTransactions",
  "description": "Retrieves multiple transactions within a specified date interval, optionally filtered by a specific account ID.includes all related detailsTransactions associated with each transaction.The function returns an array of objects,each object represents a transaction with these properties: date is when it occurred, id is its unique identifier, accountId links it to an account, amount is the total cost, payee is the recipient, notes are optional extra details, projectId optionally links it to a project, and categoryId optionally links it to a category. detailsTransactions is an array of items or services in this transaction, each with: id as its identifier, amount as its cost, transactionId linking it to the parent, name optionally identifying the item, projectId optionally linking it to a project, categoryId optionally linking it to a category, quantity optionally showing units, and unitPrice optionally giving cost per unit.",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {
      "from": {
        "type": "string",
        "description": "The start date of the interval in this format (e.g., '2024-01-01')."
      },
      "to": {
        "type": "string",
        "description": "The end date of the interval in ISO format (e.g., '2024-12-31')."
      },
      "accountId": {
        "type": "string",
        "description": "The ID of the account to filter transactions by. This field is optional."
      }
    },
    "required": [
      "from",
      "to"
    ]
  }
}{
  "name": "createOneTransaction",
  "description": "Creates a new transaction with the provided details.",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {
      "date": {
        "type": "string",
        "format": "date",
        "description": "The date of the transaction in ISO format (e.g., '2024-11-14')."
      },
      "accountId": {
        "type": "string",
        "description": "The ID of the account associated with the transaction."
      },
      "amount": {
        "type": "number",
        "description": "income are positive values,spents are negative values. The amount of the transaction  need to be Multiplied by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount) "
      },
      "payee": {
        "type": "string",
        "description": "The payee for the transaction."
      },
      "notes": {
        "type": [
          "string",
          "null"
        ],
        "description": "Optional notes or description for the transaction."
      },
      "projectId": {
        "type": [
          "string",
          "null"
        ],
        "description": "Optional project ID to associate the transaction with a specific project."
      },
      "categoryId": {
        "type": [
          "string",
          "null"
        ],
        "description": "Optional category ID to classify the transaction."
      }
    },
    "required": [
      "date",
      "accountId",
      "amount",
      "payee"
    ]
  }
}{
  "name": "createOneItem",
  "description": "Creates a new detailsTransaction with the provided details. Creates a new detailsTransaction with the provided details. All price-related values (amount and unitPrice) are multiplied by 1,000 before storage (Fixed-Point Arithmetic for storing effectively).",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {
      "transactionId": {
        "type": "string",
        "description": "The ID of the parent transaction."
      },
      "name": {
        "type": "string",
        "description": "The name or description of the detail transaction."
      },
      "quantity": {
        "type": "number",
        "description": "The quantity of the item purchased."
      },
      "unitPrice": {
        "type": "number",
        "description": "The price per unit of the item.most of the time positive.The value  need to be Multiplied by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount)"
      },
      "amount": {
        "type": "number",
        "description": "The total amount for this detail transaction (calculated as quantity * unitPrice * 1,000).income are positive values,spents are negative values. "
      },
      "projectId": {
        "type": [
          "string",
          "null"
        ],
        "description": "Optional project ID to associate the detail transaction with a specific project."
      },
      "categoryId": {
        "type": [
          "string",
          "null"
        ],
        "description": "Optional category ID to classify the detail transaction."
      }
    },
    "required": [
      "transactionId",
      "name",
      "quantity",
      "unitPrice",
      "amount"
    ]
  }
}{
  "name": "createAccount",
  "description": "Creates a new financial account with the provided name.",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string",
        "description": "The name of the financial account (e.g. Commerzbank, 'PayPal')."
      }
    },
    "required": [
      "name"
    ]
  }
}{
  "name": "createCategory",
  "description": "Creates a new financial category with the provided name and an optional monthly goal. The goal represents the monthly limit or target for earning or spending and is multiplied by 1,000 before storage.",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string",
        "description": "The name of the financial category (e.g., 'Groceries', 'Entertainment')."
      },
      "goal": {
        "type": [
          "number",
          "null"
        ],
        "description": "Optional monthly goal for the category in the base currency. The value  need to be Multiplied by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount)"
      }
    },
    "required": [
      "name"
    ]
  }
}{
  "name": "createTransactionsAndItems",
  "description": "Creates a new transaction along with all its related details. All price-related values (amount and unitPrice) must be multiplied by 1,000 before storage to adjust for precision or scaling requirements.(Fixed-Point Arithmetic for storing effectively).",
  "strict": true,
  "parameters": {
    "type": "object",
    "required": [
      "transactions"
    ],
    "properties": {
      "transactions": {
        "type": "array",
        "description": "An array of transactions, where each transaction includes its details.",
        "items": {
          "type": "object",
          "required": [
            "date",
            "accountId",
            "amount",
            "payee",
            "notes",
            "projectId",
            "categoryId",
            "detailsTransactions"
          ],
          "properties": {
            "date": {
              "type": "string",
              "description": "The date of the transaction in ISO format (e.g., '2024-11-14')."
            },
            "accountId": {
              "type": "string",
              "description": "The ID of the account associated with the transaction."
            },
            "amount": {
              "type": "number",
              "description": "The total amount of the transaction.income are positive values,spents are negative values.The value  need to be Multiplied by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount) "
            },
            "payee": {
              "type": "string",
              "description": "The payee for the transaction."
            },
            "notes": {
              "type": [
                "string",
                "null"
              ],
              "description": "Optional notes or description for the transaction."
            },
            "projectId": {
              "type": [
                "string",
                "null"
              ],
              "description": "Optional project ID to associate the transaction with a specific project."
            },
            "categoryId": {
              "type": [
                "string",
                "null"
              ],
              "description": "Optional category ID to classify the transaction."
            },
            "detailsTransactions": {
              "type": "array",
              "description": "An array of detailsTransactions related to this transaction.",
              "items": {
                "type": "object",
                "required": [
                  "amount",
                  "name",
                  "projectId",
                  "categoryId",
                  "quantity",
                  "unitPrice"
                ],
                "properties": {
                  "amount": {
                    "type": "number",
                    "description": "The amount of the detailsTransaction.income are positive values,spents are negative values.The value  need to be Multiplied by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount) "
                  },
                  "name": {
                    "type": "string",
                    "description": "The name or description of the detailsTransaction."
                  },
                  "projectId": {
                    "type": [
                      "string",
                      "null"
                    ],
                    "description": "Optional project ID to associate this detailsTransaction with a specific project."
                  },
                  "categoryId": {
                    "type": [
                      "string",
                      "null"
                    ],
                    "description": "Optional category ID to classify this detailsTransaction."
                  },
                  "quantity": {
                    "type": "number",
                    "description": "The quantity of items involved in this detailsTransaction."
                  },
                  "unitPrice": {
                    "type": "number",
                    "description": "The unit price of each item in this detailsTransaction.most of the time positif.The value  need to be Multiplied by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount)"
                  }
                },
                "additionalProperties": false
              }
            }
          },
          "additionalProperties": false
        }
      }
    },
    "additionalProperties": false
  }
}{
  "name": "createProject",
  "description": "Creates a new project with the provided details. The budget is multiplied by 1,000 before storage to adjust for precision or scaling requirements.(Fixed-Point Arithmetic for storing effectively).",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string",
        "description": "The name of the project (e.g., 'Marketing Campaign', 'Website Redesign')."
      },
      "budget": {
        "type": "number",
        "description": "The financial budget allocated for the project.income are positive values,spents are negative values.The value  need to be Multiplied by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount) "
      },
      "startDate": {
        "type": "string",
        "format": "date",
        "description": "The start date of the project in ISO format (e.g., '2024-01-01')."
      },
      "endDate": {
        "type": "string",
        "format": "date",
        "description": "The end date of the project in ISO format (e.g., '2024-06-30')."
      },
      "description": {
        "type": [
          "string",
          "null"
        ],
        "description": "Optional description or details about the project."
      }
    },
    "required": [
      "name",
      "budget",
      "startDate",
      "endDate"
    ]
  }
}{
  "name": "fetchProjects",
  "description": "Retrieves a list of all created projects. Optionally filters projects based on a specific accountId.The object represents a project with the following properties: id is the unique identifier, name is the project’s name, description is an optional description that can be null, budget is the total allocated amount, startDate is the project’s start date and can be null, endDate is the end date and can be null, spent is the total amount spent so far, and transactions is an array of associated transactions. Each transaction includes basic details such as date, id, amount, payee, and optional fields like notes, projectId, and categoryId. The detailsTransactions property represents individual items or services bought, with basic information like amount, unitPrice, and optional fields for further details.",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {
      "accountId": {
        "type": "string",
        "description": "Optional ID of the account to filter projects by. If provided, only projects associated with this account will be returned."
      }
    },
    "required": []
  }
}{
  "name": "fetchOneTransaction",
  "description": "Retrieves a specific transaction by its unique ID.includes all related detailsTransactions associated with the transaction.",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string",
        "description": "The unique identifier of the transaction to fetch."
      }
    },
    "required": [
      "id"
    ]
  }
}{
  "name": "fetchOneTransaction",
  "description": "Retrieves a specific transaction by its unique ID.includes all related detailsTransactions associated with the transaction.",
  "strict": false,
  "parameters": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string",
        "description": "The unique identifier of the transaction to fetch."
      }
    },
    "required": [
      "id"
    ]
  }
}{
  "name": "updateTransaction",
  "description": "Updates a transaction with the specified details.All price-related values (amount,...) must be multiplied by 1,000 before storage (Fixed-Point Arithmetic for storing effectively).",
  "strict": true,
  "parameters": {
    "type": "object",
    "required": [
      "id",
      "accountId",
      "amount",
      "date",
      "notes",
      "payee",
      "projectId",
      "categoryId"
    ],
    "properties": {
      "id": {
        "type": "string",
        "description": "Id of the transaction to be modified"
      },
      "accountId": {
        "type": "string",
        "description": "Identifier for the account associated with the transaction"
      },
      "amount": {
        "type": "number",
        "description": "Amount of the transaction.income are positive values,spents are negative values.The value  need to be Multiplied back by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount) "
      },
      "date": {
        "type": "string",
        "description": "Date of the transaction in ISO 8601 format"
      },
      "notes": {
        "type": "string",
        "nullable": true,
        "description": "Additional notes about the transaction"
      },
      "payee": {
        "type": "string",
        "description": "Name of the payee for the transaction"
      },
      "projectId": {
        "type": "string",
        "nullable": true,
        "description": "Identifier for the project associated with the transaction, if applicable"
      },
      "categoryId": {
        "type": "string",
        "nullable": true,
        "description": "Identifier for the category associated with the transaction, if applicable"
      }
    },
    "additionalProperties": false
  }
}{
  "name": "updateProject",
  "description": "A function that updates a project with the provided information.All price-related values (amount,...) must be multiplied by 1,000 before storage (Fixed-Point Arithmetic for storing effectively).",
  "strict": true,
  "parameters": {
    "type": "object",
    "required": [
      "name",
      "id",
      "budget",
      "startDate",
      "endDate",
      "description"
    ],
    "properties": {
      "name": {
        "type": "string",
        "description": "The name of the project"
      },
      "id": {
        "type": "string",
        "description": "Unique identifier for the project"
      },
      "budget": {
        "type": "number",
        "description": "The budget allocated for the project.income are positive values,spents are negative values.The value  need to be Multiplied back by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount) "
      },
      "startDate": {
        "type": "string",
        "description": "The starting date of the project in ISO 8601 format"
      },
      "endDate": {
        "type": "string",
        "description": "The ending date of the project in ISO 8601 format"
      },
      "description": {
        "type": "string",
        "description": "A description of the project",
        "nullable": true
      }
    },
    "additionalProperties": false
  }
}{
  "name": "updateItem",
  "description": "Updates one item (item bougth's information) with the provided item data.All price-related values (amount,...) must be multiplied by 1,000 before storage (Fixed-Point Arithmetic for storing effectively).",
  "strict": true,
  "parameters": {
    "type": "object",
    "required": [
      "id",
      "amount",
      "transactionId",
      "name",
      "quantity",
      "unitPrice",
      "categoryId",
      "projectId"
    ],
    "properties": {
      "id": {
        "type": "string",
        "description": "Unique identifier for the transaction detail"
      },
      "amount": {
        "type": "number",
        "description": "Amount for the transaction.income are positive values,spents are negative values.The value  need to be Multiplied back by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount) "
      },
      "transactionId": {
        "type": "string",
        "description": "Identifier for the transaction"
      },
      "name": {
        "type": [
          "string",
          "null"
        ],
        "description": "Name associated with the transaction, can be null"
      },
      "quantity": {
        "type": [
          "number",
          "null"
        ],
        "description": "Quantity of items in the transaction, can be null"
      },
      "unitPrice": {
        "type": [
          "number",
          "null"
        ],
        "description": "Unit price of the items, can be null.most of the time positiv.The value  need to be Multiplied back by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount)"
      },
      "categoryId": {
        "type": [
          "string",
          "null"
        ],
        "description": "Identifier for the category, can be null"
      },
      "projectId": {
        "type": [
          "string",
          "null"
        ],
        "description": "Identifier for the associated project, can be null"
      }
    },
    "additionalProperties": false
  }
}{
  "name": "updateCategory",
  "description": "Update category with specified parameters.All price-related values (amount,...) must be multiplied by 1,000 before storage (Fixed-Point Arithmetic for storing effectively).",
  "strict": true,
  "parameters": {
    "type": "object",
    "required": [
      "id",
      "name",
      "goal"
    ],
    "properties": {
      "id": {
        "type": "string",
        "description": "Unique identifier for the category"
      },
      "name": {
        "type": "string",
        "description": "Name of the category"
      },
      "goal": {
        "type": "number",
        "description": "Monthly willing spent limit or income earning (optional).The value  need to be Multiplied back by 1,000. (Fixed-Point Arithmetic for storing effectivelythe amount)",
        "nullable": true
      }
    },
    "additionalProperties": false
  }
}{
  "name": "updateAccount",
  "description": "Update account with the provided information",
  "strict": true,
  "parameters": {
    "type": "object",
    "required": [
      "id",
      "name"
    ],
    "properties": {
      "id": {
        "type": "string",
        "description": "Unique identifier of the account"
      },
      "name": {
        "type": "string",
        "description": "Name of the account holder"
      }
    },
    "additionalProperties": false
  }
}

function are in functions-ai-chat.ts