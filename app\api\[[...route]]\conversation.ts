import { Hono } from "hono";
import { z } from "zod";
import { zValidator } from "@hono/zod-validator";
import { ChatAgent, MediaItem } from "@/lib/langgraph/agent";
import { db } from "@/db/drizzle";
import { conversations, messages, media } from "@/db/schema";
import { eq, asc } from "drizzle-orm";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { createId } from "@paralleldrive/cuid2";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { existsSync } from "fs";

const conversation = new Hono()
  .post("/", async (c) => {
    const agent = new ChatAgent();
    const f = await c.req.formData();
    const question = f.get("question")?.toString() || "";
    const conversationId = f.get("conversationId")?.toString() || "";
    const userId = c.req.header("X-User-ID") || "testUser"; // Replace with actual user ID

    // TODO: Uncomment this when the database migration is fixed
     let convId = conversationId;
     if (!convId) {
       const newConversation = await db
         .insert(conversations)
         .values({ id: createId(), userId })
         .returning();
       convId = newConversation[0].id;
     }

     const userMessage = await db
       .insert(messages)
       .values({
         id: createId(),
         conversationId: convId,
         sender: "user",
         content: question,
      })
     .returning();

    // Handle file uploads
    const files = f.getAll("file");
    const mediaItems: MediaItem[] = [];
    
    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), "public", "uploads");
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true });
    }

    for (const file of files) {
      if (file instanceof File) {
        const fileName = `${createId()}_${file.name}`;
        const filePath = join(uploadsDir, fileName);
        const url = `/uploads/${fileName}`;
        
        try {
          await writeFile(filePath, Buffer.from(await file.arrayBuffer()));
          
          const mediaItem: MediaItem = {
            id: createId(),
            fileName: file.name,
            mimeType: file.type,
            url: url,
            filePath: filePath,
          };
          
          mediaItems.push(mediaItem);
          
          // TODO: Uncomment this when the database migration is fixed
           await db
             .insert(media)
             .values({
               id: mediaItem.id,
               messageId: userMessage[0].id,
              fileName: mediaItem.fileName,
               mimeType: mediaItem.mimeType,
               url: mediaItem.url,
             })
            .returning();
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);
          // Continue processing other files
        }
      }
    }

    // TODO: Uncomment this when the database migration is fixed
    const chatHistoryData = await db
      .select()
      .from(messages)
      .where(eq(messages.conversationId, convId))
      .orderBy(asc(messages.createdAt));

    // Convert database messages to LangChain BaseMessage format
    const chatHistory: BaseMessage[] = chatHistoryData.map((msg) => {
      if (msg.sender === "user") {
        return new HumanMessage(msg.content);
      } else {
        return new AIMessage(msg.content);
      }
    });

    const response = await agent.invoke({
      // TODO: Pass the actual chat history when the database is working
      messages: chatHistory,
      input: question,
      media: mediaItems,
    });

    // TODO: Uncomment this when the database migration is fixed
     await db.insert(messages).values({
       id: createId(),
       conversationId: convId,
       sender: "ai",
       content: response.output,
     });

    console.log(response);

    return c.json({ 
      response: response.output,
      mediaProcessed: mediaItems.length,
      mediaFiles: mediaItems.map(m => ({ fileName: m.fileName, mimeType: m.mimeType }))
    });
  });

export default conversation;

